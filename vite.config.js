import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'vue': 'vue/dist/vue.esm-bundler.js'
    },
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000,      // 与package.json保持一致
    open: true,
    // 配置代理解决CORS问题
    proxy: {
      // AI API代理
      '/api/v1': {
        target: 'http://***********:3006',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('AI API代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('AI API代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('AI API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // Redis代理服务
      '/redis': {
        target: 'http://***********:4001',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('Redis代理错误:', err)
          })
        }
      },
      // SSO代理服务
      '/api/sso': {
        target: 'http://***********:4003',  // 使用IPv4地址而不是localhost
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('SSO代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('SSO代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('SSO代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
})
