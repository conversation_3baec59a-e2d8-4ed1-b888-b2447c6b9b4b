/**
 * 服务器配置文件
 * 用于管理不同环境下的服务器地址配置
 */

/**
 * 获取当前环境的服务器配置
 * @returns {Object} 服务器配置对象
 */
export function getServerConfig() {
  const hostname = window.location.hostname
  const protocol = window.location.protocol
  
  // 开发环境配置
  const devConfig = {
    logServer: {
      port: 4002,
      host: hostname === 'localhost' || hostname === '127.0.0.1' ? 'localhost' : hostname,
      protocol: protocol,
      // 使用代理路径，避免CORS问题
      useProxy: true,
      proxyPath: '/api/logs'
    },
    redisProxy: {
      port: 4001,
      host: hostname === 'localhost' || hostname === '127.0.0.1' ? 'localhost' : hostname,
      protocol: protocol
    }
  }
  
  // 生产环境配置（可以根据需要修改）
  const prodConfig = {
    logServer: {
      port: 4002,
      host: hostname, // 使用当前访问的主机名
      protocol: protocol,
      // 生产环境也可以使用代理
      useProxy: true,
      proxyPath: '/api/logs'
    },
    redisProxy: {
      port: 4001,
      host: hostname,
      protocol: protocol
    }
  }
  
  // 根据环境返回配置
  const isDev = hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('dev')
  return isDev ? devConfig : prodConfig
}

/**
 * 获取日志服务器URL
 * @param {boolean} useProxy - 是否使用代理模式
 * @returns {string} 日志服务器完整URL
 */
export function getLogServerUrl(useProxy = null) {
  const config = getServerConfig()
  const { protocol, host, port, useProxy: configUseProxy, proxyPath } = config.logServer

  // 如果明确指定使用代理，或者配置中启用了代理
  const shouldUseProxy = useProxy !== null ? useProxy : configUseProxy

  if (shouldUseProxy && proxyPath) {
    // 使用代理路径，相对于当前域名
    return proxyPath
  } else {
    // 直接连接到服务器
    return `${protocol}//${host}:${port}/api/logs`
  }
}

/**
 * 获取Redis代理服务器URL
 * @returns {string} Redis代理服务器完整URL
 */
export function getRedisProxyUrl() {
  const config = getServerConfig()
  const { protocol, host, port } = config.redisProxy
  return `${protocol}//${host}:${port}/redis`
}

/**
 * 检测服务器可用性
 * @param {string} url - 服务器URL
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<boolean>} 服务器是否可用
 */
export async function checkServerAvailability(url, timeout = 5000) {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    const response = await fetch(url + '/health', {
      method: 'GET',
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    return response.ok
  } catch (error) {
    console.warn(`服务器 ${url} 不可用:`, error.message)
    return false
  }
}

/**
 * 获取可用的日志服务器URL
 * 会尝试多个可能的地址，返回第一个可用的
 * @param {boolean} preferProxy - 是否优先使用代理
 * @returns {Promise<string|null>} 可用的服务器URL，如果都不可用则返回null
 */
export async function getAvailableLogServerUrl(preferProxy = true) {
  const config = getServerConfig()
  const { protocol, port, useProxy, proxyPath } = config.logServer

  // 如果配置启用代理且优先使用代理，先尝试代理路径
  if (preferProxy && useProxy && proxyPath) {
    console.log(`🔍 检测代理服务器: ${proxyPath}`)

    const isAvailable = await checkServerAvailability(proxyPath)
    if (isAvailable) {
      console.log(`✅ 找到可用代理服务器: ${proxyPath}`)
      return proxyPath
    }
  }

  // 如果代理不可用，尝试直连服务器
  const possibleHosts = [
    window.location.hostname, // 当前访问的主机名
    'localhost',              // 本地地址
    '127.0.0.1',             // 本地IP
    // 可以添加更多可能的服务器地址
  ]

  // 去重
  const uniqueHosts = [...new Set(possibleHosts)]

  for (const host of uniqueHosts) {
    const url = `${protocol}//${host}:${port}/api/logs`
    console.log(`🔍 检测服务器: ${url}`)

    const isAvailable = await checkServerAvailability(url)
    if (isAvailable) {
      console.log(`✅ 找到可用服务器: ${url}`)
      return url
    }
  }

  console.warn('⚠️ 没有找到可用的日志服务器')
  return null
}

/**
 * 动态配置管理器
 */
export class DynamicServerConfig {
  constructor() {
    this.cachedLogServerUrl = null
    this.lastCheckTime = 0
    this.checkInterval = 5 * 60 * 1000 // 5分钟重新检测一次
  }
  
  /**
   * 获取日志服务器URL（带缓存）
   * @param {boolean} forceRefresh - 是否强制刷新
   * @param {boolean} preferProxy - 是否优先使用代理
   * @returns {Promise<string|null>} 服务器URL
   */
  async getLogServerUrl(forceRefresh = false, preferProxy = true) {
    const now = Date.now()

    // 如果有缓存且未过期，直接返回
    if (!forceRefresh &&
        this.cachedLogServerUrl &&
        (now - this.lastCheckTime) < this.checkInterval) {
      return this.cachedLogServerUrl
    }

    // 重新检测可用服务器
    this.cachedLogServerUrl = await getAvailableLogServerUrl(preferProxy)
    this.lastCheckTime = now

    return this.cachedLogServerUrl
  }
  
  /**
   * 清除缓存
   */
  clearCache() {
    this.cachedLogServerUrl = null
    this.lastCheckTime = 0
  }
}

// 创建全局配置管理器实例
export const serverConfigManager = new DynamicServerConfig()

// 导出默认配置获取函数
export default {
  getServerConfig,
  getLogServerUrl,
  getRedisProxyUrl,
  checkServerAvailability,
  getAvailableLogServerUrl,
  serverConfigManager
}
