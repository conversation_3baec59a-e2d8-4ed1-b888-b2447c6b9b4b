/**
 * Redis HTTP适配器
 * 通过HTTP API与Redis服务器交互，适用于浏览器环境
 * 注意：这需要一个Redis HTTP代理服务器，如redis-commander或自定义API
 */

import axios from 'axios'
import { getRedisConfig, generateRedisKey, getChannelName } from '../config/redis.js'
import logger from '../utils/logger.js'
import { BodorAIError, ErrorType, RetryExecutor } from '../utils/errorHandler.js'

/**
 * Redis HTTP适配器类
 * 提供Redis基本操作的HTTP接口封装
 */
class RedisHttpAdapter {
  constructor() {
    this.config = getRedisConfig()
    this.baseURL = this.getRedisHttpUrl()
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.config.connectTimeout,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.connected = false
    this.connectionPromise = null

    // 重试执行器
    this.retryExecutor = new RetryExecutor({
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000
    })

    // 设置请求和响应拦截器
    this.setupInterceptors()

    logger.info('Redis HTTP适配器已初始化', { baseURL: this.baseURL })
  }

  /**
   * 获取Redis HTTP API的URL
   * 注意：这里假设有一个Redis HTTP代理服务运行在4001端口
   * 实际部署时需要根据具体情况调整
   */
  getRedisHttpUrl() {
    // 获取当前页面的主机名
    const currentHost = window.location.hostname

    // 在开发环境中，使用当前访问的主机地址
    if (process.env.NODE_ENV === 'development') {
      return `http://${currentHost}:4001/redis`
    }

    // 生产环境中的Redis HTTP代理地址
    return `http://${this.config.host}:4001/redis`
  }

  /**
   * 设置HTTP请求拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加认证信息
        if (this.config.password) {
          config.headers.Authorization = `Bearer ${this.config.password}`
        }
        return config
      },
      (error) => {
        console.error('Redis HTTP请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        this.connected = true
        return response
      },
      (error) => {
        if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
          this.connected = false
        }
        console.error('Redis HTTP响应错误:', error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 测试连接
   */
  async ping() {
    return await this.retryExecutor.execute(async () => {
      try {
        logger.debug('执行Redis PING命令')
        const response = await this.client.get('/ping')
        this.connected = response.data === 'PONG'

        if (this.connected) {
          logger.debug('Redis PING成功')
        } else {
          logger.warn('Redis PING响应异常', { response: response.data })
        }

        return this.connected
      } catch (error) {
        this.connected = false
        logger.error('Redis PING失败', { error: error.message })
        throw new BodorAIError(
          `Redis连接失败: ${error.message}`,
          ErrorType.REDIS,
          error.code,
          { url: this.baseURL }
        )
      }
    }, { operation: 'ping' })
  }

  /**
   * 设置字符串值
   */
  async set(key, value, ttl = null) {
    try {
      const data = {
        key: generateRedisKey('session', key),
        value: typeof value === 'string' ? value : JSON.stringify(value)
      }
      
      if (ttl) {
        data.ttl = ttl
      }
      
      const response = await this.client.post('/set', data)
      return response.data.success
    } catch (error) {
      console.error('Redis SET操作失败:', error)
      throw error
    }
  }

  /**
   * 获取字符串值
   */
  async get(key) {
    try {
      const response = await this.client.get(`/get/${encodeURIComponent(generateRedisKey('session', key))}`)
      return response.data.value
    } catch (error) {
      if (error.response?.status === 404) {
        return null
      }
      console.error('Redis GET操作失败:', error)
      throw error
    }
  }

  /**
   * 删除键
   */
  async del(key) {
    try {
      const response = await this.client.delete(`/del/${encodeURIComponent(generateRedisKey('session', key))}`)
      return response.data.deleted > 0
    } catch (error) {
      console.error('Redis DEL操作失败:', error)
      throw error
    }
  }

  /**
   * 设置Hash字段
   */
  async hset(key, field, value) {
    try {
      const data = {
        key: generateRedisKey('session', key),
        field,
        value: typeof value === 'string' ? value : JSON.stringify(value)
      }
      
      const response = await this.client.post('/hset', data)
      return response.data.success
    } catch (error) {
      console.error('Redis HSET操作失败:', error)
      throw error
    }
  }

  /**
   * 获取Hash字段值
   */
  async hget(key, field) {
    try {
      const response = await this.client.get(`/hget/${encodeURIComponent(generateRedisKey('session', key))}/${encodeURIComponent(field)}`)
      return response.data.value
    } catch (error) {
      if (error.response?.status === 404) {
        return null
      }
      console.error('Redis HGET操作失败:', error)
      throw error
    }
  }

  /**
   * 获取Hash所有字段
   */
  async hgetall(key) {
    try {
      const response = await this.client.get(`/hgetall/${encodeURIComponent(generateRedisKey('session', key))}`)
      return response.data.value || {}
    } catch (error) {
      if (error.response?.status === 404) {
        return {}
      }
      console.error('Redis HGETALL操作失败:', error)
      throw error
    }
  }

  /**
   * 删除Hash字段
   */
  async hdel(key, field) {
    try {
      const response = await this.client.delete(`/hdel/${encodeURIComponent(generateRedisKey('session', key))}/${encodeURIComponent(field)}`)
      return response.data.deleted > 0
    } catch (error) {
      console.error('Redis HDEL操作失败:', error)
      throw error
    }
  }

  /**
   * 设置键的过期时间
   */
  async expire(key, ttl) {
    try {
      const data = {
        key: generateRedisKey('session', key),
        ttl
      }
      
      const response = await this.client.post('/expire', data)
      return response.data.success
    } catch (error) {
      console.error('Redis EXPIRE操作失败:', error)
      throw error
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key) {
    try {
      const response = await this.client.get(`/exists/${encodeURIComponent(generateRedisKey('session', key))}`)
      return response.data.exists
    } catch (error) {
      console.error('Redis EXISTS操作失败:', error)
      throw error
    }
  }

  /**
   * 获取匹配模式的所有键
   */
  async keys(pattern) {
    try {
      const response = await this.client.get(`/keys/${encodeURIComponent(generateRedisKey('session', pattern))}`)
      return response.data.keys || []
    } catch (error) {
      console.error('Redis KEYS操作失败:', error)
      throw error
    }
  }

  /**
   * 批量操作（Pipeline）
   */
  async pipeline(commands) {
    try {
      const data = {
        commands: commands.map(cmd => ({
          ...cmd,
          key: cmd.key ? generateRedisKey('session', cmd.key) : undefined
        }))
      }
      
      const response = await this.client.post('/pipeline', data)
      return response.data.results
    } catch (error) {
      console.error('Redis Pipeline操作失败:', error)
      throw error
    }
  }

  /**
   * 发布消息
   */
  async publish(channel, message) {
    try {
      const data = {
        channel: getChannelName(channel),
        message: typeof message === 'string' ? message : JSON.stringify(message)
      }
      
      const response = await this.client.post('/publish', data)
      return response.data.subscribers
    } catch (error) {
      console.error('Redis PUBLISH操作失败:', error)
      throw error
    }
  }

  /**
   * 获取连接状态
   */
  isConnected() {
    return this.connected
  }

  /**
   * 关闭连接
   */
  async disconnect() {
    this.connected = false
    // HTTP客户端不需要显式关闭连接
    console.log('Redis HTTP适配器已断开连接')
  }
}

// 创建单例实例
let redisAdapter = null

/**
 * 获取Redis适配器实例
 */
export const getRedisAdapter = () => {
  if (!redisAdapter) {
    redisAdapter = new RedisHttpAdapter()
  }
  return redisAdapter
}

export default RedisHttpAdapter
