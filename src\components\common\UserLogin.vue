<template>
  <div class="user-login">
    <!-- 登录按钮 -->
    <button
      v-if="!isLoggedIn"
      class="login-trigger"
      @click="showLoginDialog = true"
      title="用户登录"
    >
      <UserIcon />
      登录
    </button>

    <!-- 用户信息显示 -->
    <div v-else class="user-info" @click="showUserMenu = !showUserMenu">
      <UserIcon />
      <span class="user-name">{{ currentUser.username }}</span>
      <span class="user-id">({{ currentUser.employeeId }})</span>

      <!-- 用户菜单 -->
      <div v-if="showUserMenu" class="user-menu" @click.stop>
        <div class="user-menu-item" @click="logout">
          <LogoutIcon />
          退出登录
        </div>
      </div>
    </div>

    <!-- 强制登录遮罩层 -->
    <div v-if="forceLogin && !isLoggedIn" class="force-login-overlay">
      <div class="force-login-message">
        <h2>🔐 请先登录</h2>
        <p>您需要登录后才能使用此应用</p>
      </div>
    </div>

    <!-- 登录弹窗 -->
    <div v-if="showLoginDialog || (forceLogin && !isLoggedIn)" class="login-overlay" @click="handleOverlayClick">
      <div class="login-dialog" @click.stop>
        <div class="login-header">
          <h3>{{ forceLogin ? '请登录以继续使用' : '用户登录' }}</h3>
          <button
            v-if="!forceLogin"
            class="close-btn"
            @click="closeLoginDialog"
          >×</button>
        </div>

        <div class="login-content">
          <form @submit.prevent="handleLogin">
            <div class="form-group">
              <label for="username">用户名</label>
              <input
                id="username"
                v-model="loginForm.username"
                type="text"
                placeholder="请输入用户名"
                required
                :disabled="isLoggingIn"
              />
            </div>

            <div class="form-group">
              <label for="employeeId">工号</label>
              <input
                id="employeeId"
                v-model="loginForm.employeeId"
                type="text"
                placeholder="请输入工号"
                required
                :disabled="isLoggingIn"
              />
            </div>

            <div v-if="loginError" class="error-message">
              {{ loginError }}
            </div>

            <div class="form-actions" :class="{ 'force-login-actions': forceLogin }">
              <button
                v-if="!forceLogin"
                type="button"
                class="cancel-btn"
                @click="closeLoginDialog"
                :disabled="isLoggingIn"
              >
                取消
              </button>
              <button
                type="submit"
                class="login-btn"
                :disabled="isLoggingIn || !isFormValid"
              >
                {{ isLoggingIn ? '登录中...' : '登录' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { getUserService } from '../../services/userService.js'

// 定义props
const props = defineProps({
  forceLogin: {
    type: Boolean,
    default: false
  }
})

// 图标组件（简单的文本图标）
const UserIcon = () => '👤'
const LogoutIcon = () => '🚪'

// 响应式数据
const showLoginDialog = ref(false)
const showUserMenu = ref(false)
const isLoggingIn = ref(false)
const loginError = ref('')

// 登录表单数据
const loginForm = ref({
  username: '',
  employeeId: ''
})

// 用户服务实例
const userService = getUserService()

// 当前用户信息
const currentUser = ref(null)
const isLoggedIn = computed(() => !!currentUser.value)

// 表单验证
const isFormValid = computed(() => {
  return loginForm.value.username.trim() && loginForm.value.employeeId.trim()
})

/**
 * 处理登录
 */
const handleLogin = async () => {
  if (!isFormValid.value) {
    loginError.value = '请填写完整的用户信息'
    return
  }

  isLoggingIn.value = true
  loginError.value = ''

  try {
    const userData = {
      username: loginForm.value.username.trim(),
      employeeId: loginForm.value.employeeId.trim(),
      loginTime: new Date().toISOString()
    }

    if(userData.username !== 'admin' || userData.employeeId !== '000000'){
      throw new Error('登入禁止，只能单点登入！！！')
    }

    // 调用用户服务进行登录
    const success = await userService.login(userData)

    if (success) {
      currentUser.value = userData
      closeLoginDialog()

      // 触发登录成功事件
      emit('login-success', userData)

      console.log('用户登录成功:', userData)
    } else {
      loginError.value = '登录失败，请重试'
    }
  } catch (error) {
    console.error('登录过程出错:', error)
    loginError.value = error.message || '登录失败，请重试'
  } finally {
    isLoggingIn.value = false
  }
}

/**
 * 处理SSO登录
 * @param {string} tokenId - SSO token
 */
const handleSSOLogin = async (tokenId) => {
  if (!tokenId) {
    console.warn('SSO登录：tokenId为空')
    return
  }

  isLoggingIn.value = true
  loginError.value = ''

  try {
    console.log('开始SSO登录验证，tokenId:', tokenId)

    // 调用用户服务进行SSO登录
    const result = await userService.ssoLogin(tokenId)

    if (result.success) {
      currentUser.value = result.userData
      closeLoginDialog()

      // 触发登录成功事件
      emit('login-success', result.userData)

      // console.log('SSO登录成功:', result.userData)

      // 清除URL中的tokenId参数
      clearTokenIdFromUrl()
    } else {
      loginError.value = result.error || 'SSO登录失败'
      console.error('SSO登录失败:', result.error)
    }
  } catch (error) {
    console.error('SSO登录过程出错:', error)
    loginError.value = error.message || 'SSO登录失败，请重试'
  } finally {
    isLoggingIn.value = false
  }
}

/**
 * 从URL中获取tokenId参数
 */
const getTokenIdFromUrl = () => {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('tokenId')
}

/**
 * 清除URL中的tokenId参数
 */
const clearTokenIdFromUrl = () => {
  const url = new URL(window.location)
  url.searchParams.delete('tokenId')
  window.history.replaceState({}, document.title, url.toString())
}

/**
 * 处理退出登录
 */
const logout = async () => {
  try {
    await userService.logout()
    currentUser.value = null
    showUserMenu.value = false
    
    // 触发退出登录事件
    emit('logout')
    
    console.log('用户已退出登录')
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

/**
 * 关闭登录对话框
 */
const closeLoginDialog = () => {
  // 如果是强制登录模式且用户未登录，不允许关闭对话框
  if (props.forceLogin && !isLoggedIn.value) {
    return
  }

  showLoginDialog.value = false
  loginForm.value = {
    username: '',
    employeeId: ''
  }
  loginError.value = ''
}

/**
 * 处理遮罩层点击
 */
const handleOverlayClick = () => {
  // 如果不是强制登录模式，允许点击遮罩层关闭
  if (!props.forceLogin) {
    closeLoginDialog()
  }
}

/**
 * 点击外部关闭用户菜单
 */
const handleClickOutside = (event) => {
  if (showUserMenu.value && !event.target.closest('.user-info')) {
    showUserMenu.value = false
  }
}

// 定义事件
const emit = defineEmits(['login-success', 'logout'])

// 监听强制登录状态变化
watch(() => props.forceLogin, (newValue) => {
  if (newValue && !isLoggedIn.value) {
    // 强制登录模式下，如果用户未登录，自动显示登录对话框
    showLoginDialog.value = true
    console.log('强制登录模式：自动显示登录对话框')
  }
}, { immediate: true })

// 监听登录状态变化
watch(isLoggedIn, (newValue) => {
  if (newValue && props.forceLogin) {
    // 登录成功后，如果是强制登录模式，关闭对话框
    showLoginDialog.value = false
    console.log('用户登录成功，关闭强制登录对话框')
  }
})

// 组件挂载时检查登录状态
onMounted(async () => {
  try {
    // 首先检查URL中是否有tokenId参数（SSO登录）
    const tokenId = getTokenIdFromUrl()
    if (tokenId) {
      console.log('检测到SSO tokenId，开始自动登录:', tokenId)
      await handleSSOLogin(tokenId)
      return // SSO登录处理完成，不需要继续检查其他登录状态
    }

    // 检查现有登录状态
    const userData = await userService.getCurrentUser()
    if (userData) {
      currentUser.value = userData
      console.log('检测到已登录用户:', userData)
    } else if (props.forceLogin) {
      // 强制登录模式下，如果没有登录用户，显示登录对话框
      showLoginDialog.value = true
      console.log('强制登录模式：未检测到登录用户，显示登录对话框')
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
    if (props.forceLogin) {
      showLoginDialog.value = true
    }
  }

  // 添加全局点击事件监听
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 登录按钮 */
.login-trigger {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #495057;
  transition: all 0.2s;
}

.login-trigger:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* 用户信息显示 */
.user-info {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #e8f5e8;
  border: 1px solid #52c41a;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #389e0d;
  transition: all 0.2s;
}

.user-info:hover {
  background: #d9f7be;
}

.user-name {
  font-weight: 500;
}

.user-id {
  color: #73d13d;
  font-size: 11px;
}

/* 用户菜单 */
.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 120px;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  color: #495057;
  transition: background-color 0.2s;
}

.user-menu-item:hover {
  background: #f8f9fa;
}

/* 强制登录遮罩层 */
.force-login-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  backdrop-filter: blur(5px);
}

.force-login-message {
  text-align: center;
  color: white;
  padding: 40px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.force-login-message h2 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
}

.force-login-message p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* 登录弹窗覆盖层 */
.login-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 登录对话框 */
.login-dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.login-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #dee2e6;
}

.login-header h3 {
  margin: 0;
  color: #212529;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #495057;
}

.login-content {
  padding: 20px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.form-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

/* 错误消息 */
.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-bottom: 16px;
  padding: 8px 12px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.form-actions.force-login-actions {
  justify-content: center;
}

.cancel-btn,
.login-btn {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancel-btn {
  background: white;
  color: #6c757d;
}

.cancel-btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.login-btn {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.login-btn:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
}

.cancel-btn:disabled,
.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */

/* 平板端适配 */
@media (max-width: 1024px) {
  .user-login {
    margin-right: var(--spacing-sm);
  }

  .login-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  .user-info {
    gap: var(--spacing-sm);
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-sm);
  }

  .user-name {
    font-size: var(--font-size-sm);
  }

  .logout-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-login {
    margin-right: var(--spacing-xs);
  }

  .login-trigger {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-width: auto;
    height: 32px;
  }

  .login-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-width: auto;
  }

  .user-info {
    gap: var(--spacing-xs);
  }

  .user-avatar {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-xs);
  }

  .user-name {
    font-size: var(--font-size-xs);
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .logout-btn {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
    min-width: auto;
  }

  .login-dialog {
    width: 95%;
    max-width: 350px;
    margin: var(--spacing-md);
  }

  .login-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .login-content {
    padding: var(--spacing-md);
  }

  .form-group {
    margin-bottom: var(--spacing-sm);
  }

  .form-group input {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
  }

  .form-actions {
    gap: var(--spacing-sm);
  }

  .cancel-btn,
  .login-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .login-trigger {
    padding: var(--spacing-xs);
    font-size: 10px;
    height: 28px;
  }

  .login-btn {
    padding: var(--spacing-xs);
    font-size: 10px;
  }

  /* 移动端工具栏样式 */
  .mobile-toolbar-item .login-trigger {
    background: transparent !important;
    border: none !important;
    color: var(--text-primary) !important;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
  }

  .mobile-toolbar-item .user-info {
    background: transparent !important;
    border: none !important;
    color: var(--text-primary) !important;
    font-size: 12px;
    padding: 4px 8px;
  }

  .user-avatar {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  .user-name {
    font-size: 10px;
    max-width: 60px;
  }

  .logout-btn {
    padding: 2px var(--spacing-xs);
    font-size: 10px;
  }

  .login-dialog {
    width: 98%;
    margin: var(--spacing-xs);
  }

  .login-header {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .login-header h3 {
    font-size: var(--font-size-sm);
  }

  .login-content {
    padding: var(--spacing-sm);
  }

  .form-group input {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }

  .cancel-btn,
  .login-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
}
</style>
